#!/usr/bin/env python3
"""
Improved LinkedIn Profile Scraper with better anti-detection measures

WARNING: This script is for educational purposes only.
LinkedIn's Terms of Service prohibit automated data collection.
Use responsibly and consider LinkedIn's API for legitimate use cases.
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
import random
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class LinkedInProfile:
    """Data class to store LinkedIn profile information"""
    name: str = ""
    headline: str = ""
    location: str = ""
    about: str = ""
    experience: List[Dict] = None
    education: List[Dict] = None
    skills: List[str] = None
    certifications: List[Dict] = None
    posts: List[Dict] = None
    connections: str = ""
    profile_url: str = ""
    raw_html_saved: bool = False
    
    def __post_init__(self):
        if self.experience is None:
            self.experience = []
        if self.education is None:
            self.education = []
        if self.skills is None:
            self.skills = []
        if self.certifications is None:
            self.certifications = []
        if self.posts is None:
            self.posts = []

class ImprovedLinkedInScraper:
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """Setup session with realistic headers and settings"""
        # Rotate between different realistic User-Agent strings
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
        })
    
    def scrape_profile(self, profile_url: str, save_html: bool = True) -> LinkedInProfile:
        """
        Main method to scrape LinkedIn profile data with improved techniques
        
        Args:
            profile_url: LinkedIn profile URL
            save_html: Whether to save raw HTML for debugging
            
        Returns:
            LinkedInProfile object with scraped data
        """
        logger.info(f"Starting to scrape profile: {profile_url}")
        
        profile = LinkedInProfile(profile_url=profile_url)
        
        try:
            # Add random delay to appear more human-like
            time.sleep(random.uniform(1, 3))
            
            # Try to access LinkedIn homepage first to get cookies
            logger.info("Accessing LinkedIn homepage to establish session...")
            homepage_response = self.session.get('https://www.linkedin.com')
            
            # Add another delay
            time.sleep(random.uniform(2, 4))
            
            # Now try to access the profile
            logger.info(f"Accessing profile: {profile_url}")
            response = self.session.get(profile_url)
            response.raise_for_status()
            
            # Save raw HTML for debugging if requested
            if save_html:
                html_filename = f"linkedin_raw_{int(time.time())}.html"
                with open(html_filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                profile.raw_html_saved = True
                logger.info(f"Raw HTML saved to {html_filename}")
            
            # Check if we got blocked
            if self._is_blocked(response):
                logger.warning("Request appears to be blocked by LinkedIn")
                return profile
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract basic information using multiple strategies
            profile.name = self._extract_name(soup)
            profile.headline = self._extract_headline(soup)
            profile.location = self._extract_location(soup)
            profile.about = self._extract_about(soup)
            profile.connections = self._extract_connections(soup)
            
            # Try to extract from JSON-LD structured data
            json_data = self._extract_json_ld(soup)
            if json_data:
                self._parse_json_data(profile, json_data)
            
            # Extract sections (these might be limited without login)
            profile.experience = self._extract_experience(soup)
            profile.education = self._extract_education(soup)
            profile.skills = self._extract_skills(soup)
            profile.certifications = self._extract_certifications(soup)
            
            logger.info("Profile scraping completed")
            
        except requests.RequestException as e:
            logger.error(f"Request failed: {e}")
        except Exception as e:
            logger.error(f"Scraping failed: {e}")
        
        return profile
    
    def _is_blocked(self, response: requests.Response) -> bool:
        """Check if the request was blocked"""
        # Check for common blocking indicators
        blocking_indicators = [
            'authwall',
            'challenge',
            'captcha',
            'blocked',
            'access denied',
            'rate limit',
            'too many requests'
        ]
        
        content_lower = response.text.lower()
        for indicator in blocking_indicators:
            if indicator in content_lower:
                return True
        
        # Check status code
        if response.status_code in [403, 429, 503]:
            return True
        
        # Check if content is suspiciously short
        if len(response.text) < 1000:
            return True
        
        return False
    
    def _extract_name(self, soup: BeautifulSoup) -> str:
        """Extract profile name with multiple fallback strategies"""
        # Strategy 1: Common LinkedIn selectors
        selectors = [
            'h1.text-heading-xlarge',
            'h1.top-card-layout__title',
            '.pv-text-details__left-panel h1',
            '.ph5 h1',
            'h1[data-anonymize="person-name"]',
            '.pv-top-card--list h1',
            '.profile-photo-edit__preview h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                name = element.get_text(strip=True)
                if name and len(name) > 1:
                    return name
        
        # Strategy 2: Look for name in meta tags
        meta_selectors = [
            'meta[property="og:title"]',
            'meta[name="twitter:title"]',
            'meta[property="profile:first_name"]'
        ]
        
        for selector in meta_selectors:
            element = soup.select_one(selector)
            if element and element.get('content'):
                content = element.get('content')
                # Clean up the content (remove " | LinkedIn" etc.)
                name = re.sub(r'\s*\|\s*LinkedIn.*$', '', content).strip()
                if name and len(name) > 1:
                    return name
        
        # Strategy 3: Look in title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Extract name from title like "John Doe | LinkedIn"
            name_match = re.match(r'^([^|]+)', title_text)
            if name_match:
                name = name_match.group(1).strip()
                if name and len(name) > 1 and 'linkedin' not in name.lower():
                    return name
        
        return ""
    
    def _extract_headline(self, soup: BeautifulSoup) -> str:
        """Extract profile headline"""
        selectors = [
            '.text-body-medium.break-words',
            '.top-card-layout__headline',
            '.pv-text-details__left-panel .text-body-medium',
            '.pv-top-card--list .text-body-medium',
            'h2.pv-top-card-section__headline'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                headline = element.get_text(strip=True)
                if headline and len(headline) > 5:  # Reasonable headline length
                    return headline
        
        # Try meta description
        meta_desc = soup.select_one('meta[name="description"]')
        if meta_desc and meta_desc.get('content'):
            desc = meta_desc.get('content')
            # Extract headline from description
            if ' - ' in desc:
                potential_headline = desc.split(' - ')[0].strip()
                if len(potential_headline) > 5:
                    return potential_headline
        
        return ""
    
    def _extract_location(self, soup: BeautifulSoup) -> str:
        """Extract location information"""
        selectors = [
            '.text-body-small.inline.t-black--light.break-words',
            '.top-card-layout__first-subline',
            '.pv-text-details__left-panel .text-body-small',
            '.pv-top-card--list-bullet .text-body-small'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(strip=True)
                # Skip if it contains connection info
                if 'connection' not in text.lower() and len(text) > 2:
                    return text
        
        return ""
    
    def _extract_about(self, soup: BeautifulSoup) -> str:
        """Extract about section"""
        selectors = [
            '#about ~ .pv-shared-text-with-see-more',
            '.pv-about-section .pv-about__summary-text',
            '[data-section="summary"] .pv-about__summary-text',
            '.core-section-container__content .pv-about__summary-text'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                about = element.get_text(strip=True)
                if about and len(about) > 10:
                    return about
        
        return ""
    
    def _extract_connections(self, soup: BeautifulSoup) -> str:
        """Extract connection count"""
        selectors = [
            '.text-body-small.t-black--light',
            '.top-card-layout__first-subline'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if 'connection' in text.lower():
                    return text
        
        return ""
    
    def _extract_json_ld(self, soup: BeautifulSoup) -> Optional[Dict]:
        """Extract JSON-LD structured data"""
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                if script.string:
                    data = json.loads(script.string)
                    if isinstance(data, dict) and data.get('@type') == 'Person':
                        return data
            except json.JSONDecodeError:
                continue
        return None
    
    def _parse_json_data(self, profile: LinkedInProfile, json_data: Dict):
        """Parse JSON-LD data into profile"""
        if 'name' in json_data and not profile.name:
            profile.name = json_data['name']
        
        if 'jobTitle' in json_data and not profile.headline:
            profile.headline = json_data['jobTitle']
        
        if 'address' in json_data and not profile.location:
            address = json_data['address']
            if isinstance(address, dict):
                location_parts = []
                for key in ['addressLocality', 'addressRegion', 'addressCountry']:
                    if key in address:
                        location_parts.append(address[key])
                profile.location = ', '.join(location_parts)
    
    def _extract_experience(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract work experience (limited without login)"""
        return []  # Usually requires login
    
    def _extract_education(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract education information (limited without login)"""
        return []  # Usually requires login
    
    def _extract_skills(self, soup: BeautifulSoup) -> List[str]:
        """Extract skills (limited without login)"""
        return []  # Usually requires login
    
    def _extract_certifications(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract certifications (limited without login)"""
        return []  # Usually requires login
    
    def save_to_json(self, profile: LinkedInProfile, filename: str = None):
        """Save profile data to JSON file"""
        if filename is None:
            name = profile.name.replace(' ', '_').lower() if profile.name else 'linkedin_profile'
            filename = f"{name}_improved_data.json"
        
        profile_dict = asdict(profile)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(profile_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Profile data saved to {filename}")
        return filename

def main():
    """Main function to demonstrate usage"""
    profile_url = "https://www.linkedin.com/in/adam-zouari-263788224/"
    
    scraper = ImprovedLinkedInScraper()
    
    print(f"Scraping LinkedIn profile: {profile_url}")
    print("Note: This may take a few seconds due to anti-detection delays...")
    
    profile = scraper.scrape_profile(profile_url)
    
    # Display results
    print("\n" + "="*50)
    print("IMPROVED SCRAPER RESULTS")
    print("="*50)
    
    print(f"Name: {profile.name}")
    print(f"Headline: {profile.headline}")
    print(f"Location: {profile.location}")
    print(f"Connections: {profile.connections}")
    print(f"Raw HTML saved: {profile.raw_html_saved}")
    
    if profile.about:
        print(f"\nAbout:\n{profile.about[:200]}...")
    
    # Save to JSON
    filename = scraper.save_to_json(profile)
    print(f"\nData saved to: {filename}")
    
    if not any([profile.name, profile.headline, profile.location]):
        print("\n⚠️  No data extracted. This could be due to:")
        print("   - LinkedIn's anti-scraping measures")
        print("   - Profile privacy settings")
        print("   - Rate limiting")
        print("   - Need for authentication")
        print("\nCheck the saved HTML file for debugging.")
    
    return profile

if __name__ == "__main__":
    main()
